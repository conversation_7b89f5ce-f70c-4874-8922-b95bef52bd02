"""
Application configuration management.
"""

import os
import yaml
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseModel):
    """Database configuration."""
    url: str = Field(default="sqlite:///./carbon_news.db", description="Database URL")
    echo: bool = Field(default=False, description="Enable SQL query logging")


class APIConfig(BaseModel):
    """API server configuration."""
    host: str = Field(default="0.0.0.0", description="API host")
    port: int = Field(default=8000, description="API port")
    debug: bool = Field(default=False, description="Debug mode")


class SearchSettingsConfig(BaseModel):
    """Advanced search settings configuration."""
    include_domains: List[str] = Field(default=[], description="Domains to include in search")
    exclude_domains: List[str] = Field(
        default=["twitter.com", "facebook.com", "instagram.com", "tiktok.com"],
        description="Domains to exclude from search"
    )
    search_depth: str = Field(default="basic", description="Search depth: basic, advanced")
    extract_full_content: bool = Field(default=True, description="Extract full content from articles")
    min_content_length: int = Field(default=100, description="Minimum content length in characters")


class NewsCollectorConfig(BaseModel):
    """News collection configuration."""
    max_articles_per_source: int = Field(default=10, description="Maximum articles per source")
    default_time_range: str = Field(default="day", description="Default time range for news search")

    # Search queries for carbon regulation news
    search_queries: List[str] = Field(
        default=[
            "carbon regulations emission standards environmental policy",
            "clean energy regulations carbon policy sustainability",
            "carbon accounting standards disclosure reporting",
            "carbon pricing markets ETS carbon tax",
            "climate regulations environmental compliance"
        ],
        description="Search queries for news collection"
    )

    # Specific news sources to monitor
    specific_sources: List[str] = Field(
        default=[
            "https://www.reuters.com/sustainability/clean-energy/",
            "https://www.reuters.com/sustainability/climate-energy/",
        ],
        description="Specific news source URLs to monitor"
    )

    # Advanced search settings
    search_settings: SearchSettingsConfig = Field(default_factory=SearchSettingsConfig)


class RetrySettingsConfig(BaseModel):
    """Retry settings for failed tasks."""
    max_retries: int = Field(default=3, description="Maximum number of retries")
    retry_delay_minutes: int = Field(default=5, description="Delay between retries in minutes")


class SchedulerConfig(BaseModel):
    """Task scheduler configuration."""
    daily_run_time: str = Field(default="09:00", description="Daily task run time (HH:MM)")
    max_task_history: int = Field(default=100, description="Maximum task results to keep")
    task_timeout_minutes: int = Field(default=30, description="Task execution timeout")
    enable_auto_scheduling: bool = Field(default=True, description="Enable automatic scheduling")
    retry_settings: RetrySettingsConfig = Field(default_factory=RetrySettingsConfig)


class EmailConfig(BaseModel):
    """Email notification configuration."""
    enabled: bool = Field(default=False, description="Enable email notifications")
    smtp_server: Optional[str] = Field(default=None, description="SMTP server address")
    smtp_port: int = Field(default=587, description="SMTP server port")
    username: Optional[str] = Field(default=None, description="SMTP username")
    password: Optional[str] = Field(default=None, description="SMTP password")
    from_address: Optional[str] = Field(default=None, description="From email address")
    to_addresses: List[str] = Field(default=[], description="List of recipient email addresses")


class NotificationTriggersConfig(BaseModel):
    """Notification trigger configuration."""
    on_collection_complete: bool = Field(default=True, description="Notify when collection completes")
    on_error: bool = Field(default=True, description="Notify when errors occur")
    on_high_priority_articles: bool = Field(default=False, description="Notify for high-priority articles")
    min_articles_threshold: int = Field(default=5, description="Minimum articles to trigger notification")


class NotificationConfig(BaseModel):
    """Notification system configuration."""
    webhook_url: Optional[str] = Field(default=None, description="Webhook URL for notifications")
    slack_webhook_url: Optional[str] = Field(default=None, description="Slack webhook URL")
    enable_notifications: bool = Field(default=True, description="Enable notifications")
    email: EmailConfig = Field(default_factory=EmailConfig)
    triggers: NotificationTriggersConfig = Field(default_factory=NotificationTriggersConfig)


class ModelConfig(BaseModel):
    """Individual AI model configuration."""
    model_name: str = Field(description="OpenRouter model name")
    temperature: float = Field(default=0.1, description="Temperature for AI responses")
    max_tokens: int = Field(default=2000, description="Maximum tokens for AI responses")


class ModelsConfig(BaseModel):
    """Configuration for different AI models used by various services."""
    news_parser: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.1,
            max_tokens=2000
        ),
        description="Model for news parsing and content analysis"
    )
    url_extractor: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.0,
            max_tokens=1000
        ),
        description="Model for URL extraction from web pages"
    )
    classifier: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.1,
            max_tokens=1500
        ),
        description="Model for content classification"
    )
    summarizer: ModelConfig = Field(
        default_factory=lambda: ModelConfig(
            model_name="openai/gpt-4.1-mini",
            temperature=0.2,
            max_tokens=3000
        ),
        description="Model for summary generation"
    )


class AnalysisSettingsConfig(BaseModel):
    """Content analysis settings configuration."""
    enable_sentiment_analysis: bool = Field(default=True, description="Enable sentiment analysis")
    enable_topic_classification: bool = Field(default=True, description="Enable topic classification")
    enable_summary_generation: bool = Field(default=True, description="Enable summary generation")
    min_confidence_score: float = Field(default=0.7, description="Minimum confidence score")


class AIProcessingConfig(BaseModel):
    """AI processing configuration."""
    models: ModelsConfig = Field(default_factory=ModelsConfig)
    analysis_settings: AnalysisSettingsConfig = Field(default_factory=AnalysisSettingsConfig)


class QualityFiltersConfig(BaseModel):
    """Content quality filters configuration."""
    min_word_count: int = Field(default=50, description="Minimum word count")
    max_word_count: int = Field(default=10000, description="Maximum word count")
    require_publication_date: bool = Field(default=True, description="Require publication date")


class ContentFilteringConfig(BaseModel):
    """Content filtering configuration."""
    required_keywords: List[str] = Field(
        default=["carbon", "emission", "climate", "sustainability", "environmental", "green", "renewable"],
        description="Keywords that must be present"
    )
    priority_keywords: List[str] = Field(
        default=["regulation", "policy", "law", "mandate", "requirement", "compliance", "standard"],
        description="Keywords that indicate high priority"
    )
    exclude_keywords: List[str] = Field(
        default=["sports", "entertainment", "celebrity"],
        description="Keywords to exclude content"
    )
    languages: List[str] = Field(default=["en"], description="Accepted languages")
    quality_filters: QualityFiltersConfig = Field(default_factory=QualityFiltersConfig)


def load_yaml_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    if not config_file.exists():
        return {}

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"Warning: Could not load YAML config from {config_path}: {e}")
        return {}


class HealthCheckConfig(BaseModel):
    """Health check configuration."""
    enabled: bool = Field(default=True, description="Enable health check endpoints")
    check_interval_minutes: int = Field(default=5, description="Health check interval in minutes")


class PerformanceConfig(BaseModel):
    """Performance monitoring configuration."""
    enabled: bool = Field(default=True, description="Enable performance metrics collection")
    retention_days: int = Field(default=30, description="Metrics retention period in days")


class AlertsConfig(BaseModel):
    """Alerting thresholds configuration."""
    max_collection_time_minutes: int = Field(default=60, description="Max collection time before alert")
    max_error_rate_percent: int = Field(default=10, description="Max error rate before alert")
    max_days_without_articles: int = Field(default=2, description="Max days without articles before alert")


class MonitoringConfig(BaseModel):
    """Monitoring and health check configuration."""
    health_check: HealthCheckConfig = Field(default_factory=HealthCheckConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    alerts: AlertsConfig = Field(default_factory=AlertsConfig)


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(default="development", description="Environment (development/production)")
    debug: bool = Field(default=True, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")

    # API Keys
    openrouter_api_key: Optional[str] = Field(default=None, description="OpenRouter API key")
    tavily_api_key: Optional[str] = Field(default=None, description="Tavily API key")

    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    news_collector: NewsCollectorConfig = Field(default_factory=NewsCollectorConfig)
    scheduler: SchedulerConfig = Field(default_factory=SchedulerConfig)
    notifications: NotificationConfig = Field(default_factory=NotificationConfig)
    ai_processing: AIProcessingConfig = Field(default_factory=AIProcessingConfig)
    content_filtering: ContentFilteringConfig = Field(default_factory=ContentFilteringConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)

    def __init__(self, **kwargs):
        # Load YAML configuration first
        yaml_config = load_yaml_config()

        # Create a clean config dict with only valid Settings fields
        merged_config = {}

        # Copy valid fields from YAML config (excluding API keys for security)
        valid_fields = {
            'environment', 'debug', 'log_level',
            'database', 'api', 'news_collector', 'scheduler', 'notifications',
            'ai_processing', 'content_filtering', 'monitoring'
        }

        for key, value in yaml_config.items():
            if key in valid_fields:
                merged_config[key] = value

        # API keys are ONLY loaded from environment variables for security
        # They should never be stored in YAML files that might be committed to version control

        # Merge with kwargs, giving priority to kwargs
        merged_config.update(kwargs)

        super().__init__(**merged_config)

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"
        case_sensitive = False


# Global settings instance
_settings_instance = None


def get_settings() -> Settings:
    """Get application settings."""
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings()
    return _settings_instance


def reload_settings() -> Settings:
    """Reload settings from configuration files."""
    global _settings_instance
    _settings_instance = Settings()
    return _settings_instance


# Initialize settings on module import
settings = get_settings()
